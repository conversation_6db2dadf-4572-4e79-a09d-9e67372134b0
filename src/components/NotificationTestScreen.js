import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { setIndividualNotification } from '../redux/slices/notificationSlice';
import LocalNotificationService from '../utils/localNotifactionService';

const NotificationTestScreen = () => {
  const dispatch = useDispatch();
  const individualNotifications = useSelector(
    (state) => state.notifications.individualNotifications,
  );
  const showNotifications = useSelector(
    (state) => state.profile.showNotifications,
  );

  const modalities = ['THC inhaled', 'THC oral', 'CBD inhaled', 'CBD oral'];

  const toggleNotification = async (modality) => {
    const currentState = individualNotifications[modality];
    const newState = !currentState;
    
    dispatch(setIndividualNotification({
      modality,
      enabled: newState
    }));

    if (newState && showNotifications) {
      // Schedule a test notification in 5 seconds
      await LocalNotificationService.requestPermission(true);
      await LocalNotificationService.scheduleLockoutNotification(modality, 0.0014); // ~5 seconds
      console.log(`Test notification scheduled for ${modality}`);
    } else {
      LocalNotificationService.cancelModalityNotification(modality);
      console.log(`Cancelled notifications for ${modality}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Individual Notification Settings</Text>
      <Text style={styles.subtitle}>
        Global Notifications: {showNotifications ? 'ON' : 'OFF'}
      </Text>
      
      {modalities.map((modality) => (
        <TouchableOpacity
          key={modality}
          style={[
            styles.button,
            { backgroundColor: individualNotifications[modality] ? '#4AAE9B' : '#e0e0e0' }
          ]}
          onPress={() => toggleNotification(modality)}
        >
          <Text style={[
            styles.buttonText,
            { color: individualNotifications[modality] ? '#fff' : '#555' }
          ]}>
            {modality}: {individualNotifications[modality] ? 'ON' : 'OFF'}
          </Text>
        </TouchableOpacity>
      ))}
      
      <Text style={styles.note}>
        Toggle any notification to test. When enabled, a test notification will be scheduled in 5 seconds.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  button: {
    padding: 15,
    marginVertical: 5,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  note: {
    marginTop: 20,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default NotificationTestScreen;
