import { useContext, useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  PanResponder,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ThemeContext } from '../context/ThemeContext';
import { ypdBlack, ypdGreen } from '../utils/colors';
import { useSelector, useDispatch } from 'react-redux';
import LocalNotificationService from '../utils/localNotifactionService';
import { setIndividualNotification } from '../redux/slices/notificationSlice';

const CustomHomeScreenButton = ({
  title,
  dosage,
  onPress,
  iconName,
  textColor,
  fontSize,
  fontFamily,
  iconSize = 24,
  disabled,
  style,
  iconStyle,
  alignSelf,
  showDivider = true,
  modalParams,
  modality,
  lockoutHours,
}) => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();

  const showNotifications = useSelector(
    (state) => state.profile.showNotifications,
  );

  const individualNotifications = useSelector(
    (state) => state.notifications.individualNotifications,
  );

  // Get the modality from modalParams if available, otherwise use the modality prop
  const currentModality = modalParams?.modality || modality;
  const isModalityNotificationEnabled = individualNotifications[currentModality] ?? true;

  const [reminder, setReminder] = useState(showNotifications && isModalityNotificationEnabled);

  useEffect(() => {
    setReminder(showNotifications && isModalityNotificationEnabled);
  }, [showNotifications, isModalityNotificationEnabled]);

  const handleReminderChange = async (newValue) => {
    setReminder(newValue);

    // Update individual notification setting for this modality
    dispatch(setIndividualNotification({
      modality: currentModality,
      enabled: newValue
    }));

    if (newValue && showNotifications) {
      console.log(`setting local notifications for ${currentModality} ✅`);
      await LocalNotificationService.requestPermission(true);
      await LocalNotificationService.scheduleLockoutNotification(
        currentModality,
        modalParams?.lockoutHours || lockoutHours,
      );
    } else {
      // Cancel only this modality's notification
      LocalNotificationService.cancelModalityNotification(currentModality);
      console.log(`cancel local notifications for ${currentModality} ❌`);
    }
  };

  const animX = useRef(new Animated.Value(reminder ? 1 : 0)).current;
  const startValue = useRef(0);

  useEffect(() => {
    Animated.timing(animX, {
      toValue: reminder ? 1 : 0,
      duration: 250,
      useNativeDriver: false,
    }).start();
  }, [reminder]);

  const translateX = animX.interpolate({
    inputRange: [0, 1],
    outputRange: [2, 37],
  });

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        startValue.current = animX.__getValue();
      },
      onPanResponderMove: (_, gestureState) => {
        let raw = startValue.current * 37 + gestureState.dx;
        raw = Math.min(Math.max(raw, 0), 37);
        animX.setValue(raw / 37);
      },
      onPanResponderRelease: () => {
        animX.stopAnimation((current) => {
          const isOn = current > 0.5;
          Animated.spring(animX, {
            toValue: isOn ? 1 : 0,
            useNativeDriver: false,
            friction: 6,
            tension: 80,
          }).start(() => {
            handleReminderChange(isOn);
          });
        });
      },
    }),
  ).current;

  const isTimer =
    disabled &&
    (title?.toLowerCase().includes('oral') ||
      title?.toLowerCase().includes('inhaled'));

  let mainTitle = title;
  let subtitle = null;

  if (isTimer) {
    const cleanTitle = title.trim();
    const match = cleanTitle.match(/(\d{1,2}:\d{2}(:\d{2})?)$/);

    let hours = '00',
      minutes = '00',
      seconds = '00';

    if (match) {
      const timePart = match[0];
      const parts = timePart.split(':');

      if (parts.length === 3) {
        [hours, minutes, seconds] = parts;
      } else if (parts.length === 2) {
        [minutes, seconds] = parts;
      }

      mainTitle = cleanTitle.replace(timePart, '').trim();
      subtitle = `You can take your next dose in ${hours}hr ${minutes}min ${seconds}sec`;
    }
  }

  return (
    <TouchableOpacity
      style={[
        { padding: 12, borderRadius: 8, backgroundColor: 'white' },
        style,
      ]}
      onPress={() => onPress?.(modalParams)}
      disabled={disabled && isTimer}
    >
      <View>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Text
            style={{
              fontSize,
              fontFamily,
              color: textColor || '#333',
              flexShrink: 1,
            }}
          >
            {mainTitle}
          </Text>

          {iconName ? (
            <Ionicons
              name={iconName}
              size={iconSize}
              color={theme.dark ? ypdGreen : ypdBlack}
              style={[{ marginLeft: 8 }, iconStyle]}
            />
          ) : null}
        </View>

        {subtitle && (
          <Text
            style={{
              marginTop: 4,
              fontSize: 11,
              color: '#4AAE9B',
            }}
          >
            {subtitle}
          </Text>
        )}

        {isTimer && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#F5F5F5',
              justifyContent: 'flex-end',
              paddingVertical: 10,
              paddingHorizontal: 16,
              opacity: 0.95,
              marginTop: 10,
            }}
          >
            <Text style={{ fontSize: 12, fontWeight: '500', marginRight: 8 }}>
              Remind me
            </Text>

            <TouchableOpacity
              onPress={() => handleReminderChange(!reminder)}
              activeOpacity={0.8}
            >
              <View
                style={{
                  width: 60,
                  height: 18,
                  borderRadius: 10,
                  backgroundColor: reminder ? '#4AAE9B' : '#e0e0e0',
                  justifyContent: 'center',
                }}
              >
                <Text
                  style={{
                    fontSize: 12,
                    fontWeight: '600',
                    color: reminder ? '#fff' : '#555',
                    position: 'absolute',
                    left: reminder ? 6 : 'auto',
                    right: reminder ? 'auto' : 6,
                  }}
                >
                  {reminder ? 'ON' : 'OFF'}
                </Text>

                <Animated.View
                  {...panResponder.panHandlers}
                  style={{
                    position: 'absolute',
                    top: '50%',
                    marginTop: -13,
                    transform: [{ translateX }],
                    width: 26,
                    height: 26,
                    borderRadius: 13,
                    backgroundColor: '#fff',
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowColor: '#000',
                    shadowOpacity: 0.15,
                    shadowRadius: 3,
                    elevation: 3,
                  }}
                >
                  <Icon
                    name={
                      reminder ? 'notifications-active' : 'notifications-none'
                    }
                    size={18}
                    color={reminder ? '#4AAE9B' : '#999'}
                  />
                </Animated.View>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {showDivider && !isTimer && (
        <View style={{ height: 1, backgroundColor: '#eee', marginTop: 10 }} />
      )}
    </TouchableOpacity>
  );
};

export default CustomHomeScreenButton;
