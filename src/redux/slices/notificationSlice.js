import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  appStateCheck: false,
  dailyReminders: false,
  morningReminder: {
    hour: 9,
    min: 0,
  },
  eveningReminder: {
    hour: 19,
    min: 0,
  },
  individualNotifications: {
    'THC inhaled': true,
    'THC oral': true,
    'CBD inhaled': true,
    'CBD oral': true,
  },
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setDailyReminders(state, action) {
      state.dailyReminders = action.payload;
    },
    setMorningReminder(state, action) {
      state.morningReminder = action.payload;
    },
    setEveningReminder(state, action) {
      state.eveningReminder = action.payload;
    },
    setAppStateCheck(state, action) {
      state.appStateCheck = action.payload;
    },
    setIndividualNotification(state, action) {
      const { modality, enabled } = action.payload;
      state.individualNotifications[modality] = enabled;
    },
  },
});

export const {
  setDailyReminders,
  setMorningReminder,
  setEveningReminder,
  setAppStateCheck,
  setIndividualNotification,
} = notificationSlice.actions;

export default notificationSlice.reducer;
